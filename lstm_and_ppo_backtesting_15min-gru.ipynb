{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5145bb4c-8809-4e2b-8c16-9c4f225df26f", "metadata": {}, "outputs": [], "source": ["%reset -f"]}, {"cell_type": "code", "execution_count": 2, "id": "d1cce646-31f9-466c-9b69-1c2ca17bcc60", "metadata": {}, "outputs": [], "source": ["import gymnasium as gym\n", "import numpy as np\n", "import pandas as pd\n", "from gymnasium import spaces\n", "import requests\n", "#import pandas_ta as ta\n", "from sklearn.preprocessing import MinMaxScaler\n", "from oandapyV20 import API\n", "from oandapyV20.endpoints import instruments, pricing\n", "from oandapyV20.endpoints.instruments import InstrumentsCandles\n", "from oandapyV20.exceptions import V20Error\n", "import logging\n", "from stable_baselines3 import PPO\n", "from stable_baselines3.common.env_util import make_vec_env\n", "from stable_baselines3.common.vec_env import VecNormalize\n", "from stable_baselines3.common.monitor import Monitor\n", "from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize, VecMonitor\n", "from stable_baselines3.common.callbacks import EvalCallback, StopTrainingOnRewardThreshold\n", "from oandapyV20.endpoints.pricing import PricingStream\n", "import torch\n", "import torch.nn as nn\n", "import ta \n", "from typing import Dict, Tuple, List, Optional\n", "from transformers import pipeline,BertTokenizer,BertForSequenceClassification\n", "import subprocess\n", "import mkl\n", "from sklearn.preprocessing import MinMaxScaler\n", "from torch.utils.data import TensorDataset, DataLoader\n", "from pathlib import Path\n", "from joblib import dump,load"]}, {"cell_type": "code", "execution_count": 3, "id": "03b6d180-a87e-49f3-887c-3f45660cdf60", "metadata": {}, "outputs": [], "source": ["# Configuration\n", "ACCESS_TOKEN = \"5195a5aa58953e97d982e4c17ef761a7-2dfb4f0ca8739cc2cd9883d27e56532c\"\n", "#access_token=ACCESS_TOKEN \n", "ACCOUNT_ID = \"101-001-********-001\"\n", "account_id=ACCOUNT_ID \n", "INSTRUMENT = \"AUD_CHF\"\n", "instrument = INSTRUMENT\n", "TRADE_SIZE_PERCENT = 2  # Risk per trade (% of equity)\n", "STOP_LOSS_PIPS = 20\n", "TAKE_PROFIT_PIPS = 40\n", "LEVERAGE = 50  # Added leverage for margin calculation\n", "TAKE_PROFIT_PIPS = 40\n", "PIP_VALUE = 0.0001\n", "api = API(access_token=ACCESS_TOKEN)  # Make sure you have this defined\n", "#API_KEY = \"qm99hmrlm5tt3bvei6rwoopvrelv5bpm3fwtzln0\"\n", "senti_instru = INSTRUMENT.replace('_','-')\n", "url= f\"https://forexnewsapi.com/api/v1?currencypair={senti_instru}&items=3&page=1&token=qm99hmrlm5tt3bvei6rwoopvrelv5bpm3fwtzln0\""]}, {"cell_type": "code", "execution_count": 4, "id": "b617ece3-66fd-4612-b898-e601e35778d8", "metadata": {}, "outputs": [], "source": ["# Initialize logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "class LSTMPredictor(nn.Mo<PERSON>le):\n", "    def __init__(self, input_size, hidden_size=64, num_layers=2, dropout=0.3):\n", "        super().__init__()\n", "        self.gru = nn.GRU(\n", "            input_size=input_size,\n", "            hidden_size=hidden_size,\n", "            num_layers=num_layers,\n", "            batch_first=True,\n", "            dropout=dropout if num_layers > 1 else 0.0\n", "        )\n", "        self.fc = nn.Linear(hidden_size, 3)\n", "\n", "    def forward(self, x):\n", "        _, hn = self.gru(x)\n", "        out = hn[-1]\n", "        return torch.sigmoid(self.fc(out))\n", "class FeatureGenerator:\n", "    def __init__(self, price_history, smoothing_factor=0.1):\n", "        self.price_history = price_history\n", "        self.smoothing_factor = smoothing_factor\n", "        self.smoothed_prices = self._smooth_prices()\n", "        self.normalized_smoothed = self._normalize(self.smoothed_prices)\n", "        self.window_size = 20\n", "\n", "    def _smooth_prices(self):\n", "        smoothed_prices = [self.price_history[0]]  # Start with the first price as is\n", "        for i in range(1, len(self.price_history)):\n", "            smoothed_price = (1 - self.smoothing_factor) * smoothed_prices[-1] + self.smoothing_factor * self.price_history[i]\n", "            smoothed_prices.append(smoothed_price)\n", "        return smoothed_prices\n", "\n", "    def _normalize(self, prices):\n", "        prices = np.array(prices)\n", "        return (prices - prices.mean()) / (prices.std() + 1e-8)\n", "\n", "    def get_smoothed_prices(self):\n", "        return self.smoothed_prices\n", "    \n", "    def get_normalized_smoothed_prices(self):\n", "        prices = np.array(self.smoothed_prices)\n", "        return (prices - prices.mean()) / (prices.std() + 1e-8)\n", "\n", "    def get_features(self, idx):\n", "        # Provide a sliding window of normalized smoothed prices as features\n", "        start_idx = max(0, idx - self.window_size + 1)\n", "        window = self.normalized_smoothed[start_idx:idx + 1]\n", "        \n", "        # Pad with zeros if window is smaller than expected\n", "        if len(window) < self.window_size:\n", "            window = np.pad(window, (self.window_size - len(window), 0))\n", "        \n", "        # Add 4 additional dummy features to meet the expected shape (24,)\n", "        extra_features = np.array([0.0, 0.0, 0.0, 0.0])  # Replace with actual indicators if needed\n", "        \n", "        return np.concatenate([window, extra_features])\n", "\n", "from joblib import load\n", "import torch\n", "\n", "class ForexTradingEnv(gym.Env):\n", "    def __init__(self):\n", "        super(ForexTradingEnv, self).__init__()\n", "    \n", "        # --- Spaces (define before anything else) ---\n", "        self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(24,), dtype=np.float32)\n", "        self.action_space = spaces.Discrete(3)\n", "    \n", "        # --- API / Trading Params ---\n", "        self.api = API(access_token=ACCESS_TOKEN, environment=\"practice\")\n", "        self.equity = 100000\n", "        self.position = None\n", "        self.trade_size = 0\n", "        self.commission = 0.00002\n", "        self.stop_out_level = 0.5 * self.equity\n", "        self.equity_history = [self.equity]\n", "    \n", "        # --- Brownian motion ---\n", "        self.bm_mu = 0.001\n", "        self.bm_sigma = 0.01\n", "    \n", "        # --- Models and Data ---\n", "        self.scaler = MinMaxScaler()\n", "        try:\n", "            with open(CONFIG_PATH) as f:\n", "                cfg = json.load(f)\n", "            valid_keys = {'input_size', 'hidden_size', 'num_layers', 'dropout'}\n", "            clean_cfg = {k: cfg[k] for k in cfg if k in valid_keys}\n", "        \n", "            self.lstm_model = LSTMPredictor(**clean_cfg)\n", "            self.lstm_model.load_state_dict(torch.load(LSTM_MODEL_PATH, map_location=torch.device(\"cpu\")))\n", "            self.lstm_model.eval()\n", "        \n", "            self.lstm_scaler = load(SCALER_PATH)\n", "            self.lstm_output_scaler = load(\"saved_models/lstm_output_scaler.joblib\")\n", "        except Exception as e:\n", "            print(\"? Failed to load LSTM or scalers:\", e)\n", "            \n", "        # --- Indicator logic ---\n", "        self.indicator_cache = {}\n", "        self.indicator_update_frequency = 5\n", "        self._last_indicator_step = -5\n", "    \n", "        # --- Historical Data ---\n", "        try:\n", "            self.price_history = self._init_price_history()\n", "            self.feature_generator = FeatureGenerator(self.price_history)\n", "            self.data = pd.read_csv(\"AUD_CHF_data.csv\")\n", "        except Exception as e:\n", "            print(\"?? Failed to load data/indicators:\", e)\n", "    \n", "        self.current_step = 50\n", "    \n", "        self.seed()\n", "        self.reset()\n", "         \n", "    \n", "    \n", "    \n", "    \n", "    def seed(self, seed=None):\n", "        self.np_random, seed = gym.utils.seeding.np_random(seed)\n", "        return [seed]\n", "        \n", "            # Feature names\n", "        self.feature_names = [\n", "                # Core price info (3)\n", "                'bid', 'ask', 'spread',\n", "                \n", "                # Technical indicators (14)\n", "                'rsi', 'macd', \n", "                'bollinger_upper', 'bollinger_middle', 'bollinger_lower',\n", "                'atr', 'sar', 'stoch_k', 'stoch_d',\n", "                'adx', 'cci', 'obv', 'vwap',\n", "                \n", "                # Brownian motion (3)\n", "                'bm_mu', 'bm_sigma', 'bm_last_move',\n", "                \n", "                # LSTM predictions (3)\n", "                'lstm_mean', 'lstm_lower', 'lstm_upper'\n", "                # market sentiment\n", "                #'sentiment'\n", "                ]\n", "           \n", "\n", "    def reset(self, *, seed=None, options=None):\n", "        super().reset(seed=seed)\n", "    \n", "        self.current_step = 0\n", "        self.equity_history = [self.equity]\n", "        self.price_history = self._init_price_history()\n", "        self.tech_indicators = self._init_tech_indicators()\n", "    \n", "        # ?? Force indicator update for the first observation\n", "        self._last_indicator_step = -self.indicator_update_frequency\n", "        self._update_tech_indicators()\n", "    \n", "        # Sentiment setup\n", "        if getattr(self, \"simulate_sentiment\", False):\n", "            self.live_sentiment = np.random.choice([-1.0, 0.0, 1.0])\n", "        else:\n", "            self.live_sentiment = 0.0\n", "    \n", "        obs = self._next_observation()\n", "        return obs, {}\n", "\n", "    def _init_price_history(self):\n", "        \"\"\"Initialize price history with Brownian motion\"\"\"\n", "        n = 100  # History length\n", "        prices = np.ones(n) * 1.1000  # Start at 1.1000\n", "        for i in range(1, n):\n", "            prices[i] = prices[i-1] * np.exp((self.bm_mu - 0.5*self.bm_sigma**2) + \n", "                                           self.bm_sigma * np.random.normal())\n", "        return prices.tolist()\n", "       \n", "    def _init_tech_indicators(self, force_refresh=False):\n", "        \"\"\"Initialize all technical indicators using smoothed prices from FeatureGenerator\"\"\"\n", "        \n", "        if not force_refresh and hasattr(self, \"_cached_indicators\") and self._cached_indicators is not None:\n", "            return self._cached_indicators\n", "    \n", "        try:\n", "            # Use smoothed prices from feature generator\n", "            smoothed_prices = self.feature_generator.get_smoothed_prices()\n", "    \n", "            if len(smoothed_prices) < 20:\n", "                return {}\n", "    \n", "            # Create synthetic OHLC data\n", "            df = pd.DataFrame({\n", "                'price': smoothed_prices,\n", "                'high': [p * 1.0005 for p in smoothed_prices],\n", "                'low': [p * 0.9995 for p in smoothed_prices]\n", "            })\n", "    \n", "            # Bollinger Bands\n", "            df['bollinger_middle'] = df['price'].rolling(20).mean()\n", "            std = df['price'].rolling(20).std()\n", "            df['bollinger_upper'] = df['bollinger_middle'] + 2 * std\n", "            df['bollinger_lower'] = df['bollinger_middle'] - 2 * std\n", "    \n", "            # Other Indicators\n", "            df['rsi'] = ta.momentum.RSIIndicator(df['price'], window=14).rsi()\n", "            df['macd'] = ta.trend.MACD(df['price']).macd()\n", "            df['atr'] = ta.volatility.AverageTrueRange(df['high'], df['low'], df['price'], window=14).average_true_range()\n", "            df['sar'] = ta.trend.PSARIndicator(df['high'], df['low'], df['price']).psar()\n", "            stoch = ta.momentum.StochasticOscillator(df['high'], df['low'], df['price'], window=14, smooth_window=3)\n", "            df['stoch_k'] = stoch.stoch()\n", "            df['stoch_d'] = stoch.stoch_signal()\n", "            df['adx'] = ta.trend.ADXIndicator(df['high'], df['low'], df['price'], window=14).adx()\n", "            df['cci'] = ta.trend.CCIIndicator(df['high'], df['low'], df['price'], window=20).cci()\n", "            df['obv'] = ta.volume.OnBalanceVolumeIndicator(df['price'], df['price'].diff().fillna(0)).on_balance_volume()\n", "            df['vwap'] = ta.volume.VolumeWeightedAveragePrice(\n", "                df['high'], df['low'], df['price'], df['price'].diff().abs().fillna(0)\n", "            ).volume_weighted_average_price()\n", "    \n", "            # Fill missing values\n", "            df.fillna(0, inplace=True)\n", "    \n", "            # Cache the latest row\n", "            self._cached_indicators = df.iloc[-1].to_dict()\n", "            return self._cached_indicators\n", "    \n", "        except Exception as e:\n", "            logger.error(f\"Error initializing technical indicators: {e}\")\n", "            return {}\n", "\n", "\n", "    def load_lstm_model(self):\n", "        \"\"\"Load pretrained LSTM model\"\"\"\n", "        try:\n", "            self.lstm_model.load_state_dict(torch.load('lstm_predictor.pth'))\n", "            self.lstm_model.eval()\n", "        except Exception as e:\n", "            logger.warning(f\"Could not load LSTM model: {e}\")\n", "\n", "    \"\"\"def step(self, action):\n", "        try:\n", "            self._take_action(action)\n", "            self.current_step += 1\n", "    \n", "            bid, ask = self._get_current_prices()  # ? Don't use live API in training\n", "            self._update_price_history(bid)\n", "    \n", "            if self.current_step - self._last_indicator_step >= self.indicator_update_frequency:\n", "                self._update_tech_indicators()\n", "                self._last_indicator_step = self.current_step\n", "    \n", "            self._check_position_conditions(bid, ask)\n", "            reward = self._calculate_reward()\n", "    \n", "            terminated = self.equity <= self.stop_out_level\n", "            truncated = self.current_step >= 1000  # Or your max steps per episode\n", "            info = {}\n", "    \n", "            obs = self._next_observation()\n", "            return obs, reward, terminated, truncated, info\n", "    \n", "        except Exception as e:\n", "            logger.error(f\"Error in step: {e}\")\n", "            obs = np.zeros(self.observation_space.shape, dtype=np.float32)\n", "    \n", "            return obs, 0.0, <PERSON>, <PERSON>alse, {}\"\"\"\n", "\n", "\n", "    def step(self, action):\n", "        try:\n", "            self._take_action(action)\n", "            self.current_step += 1\n", "    \n", "            # Simulate bid/ask from CSV\n", "            price = self.data['c'].iloc[self.current_step]\n", "            bid = price\n", "            ask = price + 0.0002  # Simulated spread\n", "    \n", "            self._update_price_history(price)\n", "    \n", "            if self.current_step - self._last_indicator_step >= self.indicator_update_frequency:\n", "                self._update_tech_indicators()\n", "                self._last_indicator_step = self.current_step\n", "                #print(f\"[Indicators] {self.indicator_cache}\")\n", "                \n", "    \n", "            self._check_position_conditions(bid, ask)\n", "    \n", "            reward = self._calculate_reward()\n", "            terminated = self.equity <= self.stop_out_level\n", "            truncated = self.current_step >= len(self.data) - 1\n", "            info = {}\n", "    \n", "            obs = self._next_observation()\n", "            return obs, reward, terminated, truncated, info\n", "    \n", "        except Exception as e:\n", "            logger.error(f\"Error in step: {e}\")\n", "            obs = np.zeros(self.observation_space.shape, dtype=np.float32)\n", "            logger.info(f\"Step: {self.current_step}, Action: {action}\")\n", "            print(f\"[STEP {self.current_step}] Action: {action}, Reward: {reward:.4f}, Equity: {self.equity:.2f}\")\n", "            #print(f\"         LSTM Prediction: {self._get_lstm_prediction()}\")\n", "            #print(f\"         Position: {self.position}\")\n", "            return obs, 0.0, True, <PERSON>alse, {}\n", "    \n", "    def _next_observation(self):\n", "        \"\"\"Generate the next observation with all 25 features (including sentiment)\"\"\"\n", "    \n", "        # 1. Core price info (3)\n", "        bid, ask = self._get_current_prices()\n", "        price_info = np.array([bid, ask, ask - bid], dtype=np.float32)\n", "    \n", "        # 2. Technical indicators from cache (14)\n", "        tech_indicators = np.array([\n", "            self.indicator_cache.get('rsi', 0.0),\n", "            self.indicator_cache.get('macd', 0.0),\n", "            self.indicator_cache.get('bb_upper', 0.0),\n", "            (self.indicator_cache.get('bb_upper', 0.0) + self.indicator_cache.get('bb_lower', 0.0)) / 2,\n", "            self.indicator_cache.get('bb_lower', 0.0),\n", "            self.indicator_cache.get('atr', 0.0),\n", "            self.indicator_cache.get('sar', 0.0),\n", "            self.indicator_cache.get('stoch_k', 0.0),\n", "            self.indicator_cache.get('stoch_d', 0.0),\n", "            self.indicator_cache.get('i<PERSON><PERSON><PERSON>', 0.0),\n", "            self.indicator_cache.get('adx', 0.0),\n", "            self.indicator_cache.get('cci', 0.0),\n", "            self.indicator_cache.get('obv', 0.0),\n", "            self.indicator_cache.get('vwap', 0.0),\n", "        ], dtype=np.float32)\n", "    \n", "        # 3. Brownian motion features (3)\n", "        bm_features = np.array(self._get_brownian_features(), dtype=np.float32)\n", "    \n", "        # 4. LSTM predictions (3)\n", "        lstm_preds = np.array(self._get_lstm_prediction(), dtype=np.float32)\n", "    \n", "        # 5. Sentiment feature (1)  default to neutral\n", "        if getattr(self, \"simulate_sentiment\", False):\n", "            sentiment_score = np.random.choice([-1.0, 0.0, 1.0])\n", "        else:\n", "            sentiment_score = getattr(self, \"live_sentiment\", 0.0)\n", "        sentiment_feature = np.array([sentiment_score], dtype=np.float32)\n", "    \n", "        # Combine all (3 + 14 + 3 + 3 + 1 = 24)\n", "        obs = np.concatenate([\n", "            price_info,\n", "            tech_indicators,\n", "            bm_features,\n", "            lstm_preds,\n", "            sentiment_feature\n", "        ])\n", "    \n", "        if obs.shape[0] != 24:\n", "            logger.error(f\"Observation shape mismatch: {obs.shape[0]} != 24\")\n", "            return np.zeros(24, dtype=np.float32)\n", "       # print(f\"[Observation] {obs}\")\n", "        return obs\n", "\n", "    def _get_brownian_features(self):\n", "        \"\"\"Calculate Brownian motion features\"\"\"\n", "        if len(self.price_history) < 2:\n", "            return [0, 0, 0]\n", "        \n", "        returns = np.diff(np.log(self.price_history[-50:]))\n", "        mu = np.mean(returns)\n", "        sigma = np.std(returns)\n", "        last_move = returns[-1] if len(returns) > 0 else 0\n", "        return [mu, sigma, last_move]\n", "\n", "    def _get_lstm_prediction(self):\n", "        if self.current_step < 30:\n", "            return [0.0, 0.0, 0.0]\n", "    \n", "        try:\n", "            # Create DataFrame from price history\n", "            df = pd.DataFrame({\n", "                'c': self.price_history[-30:]\n", "            })\n", "    \n", "            # Add technical indicators\n", "            df = LSTMTrainer.add_technical_indicators(df)  # must include logic for macd, macd_signal, etc.\n", "    \n", "            # Select only the 6 features used during training\n", "            feature_cols = ['c', 'macd', 'macd_signal', 'ema_fast', 'ema_slow', 'e2']\n", "            features = df[feature_cols].values\n", "    \n", "            # Ensure exactly 30 rows\n", "            if features.shape[0] < 30:\n", "                pad_len = 30 - features.shape[0]\n", "                features = np.pad(features, ((pad_len, 0), (0, 0)), mode='constant')\n", "    \n", "            # Scale using trained input scaler\n", "            X_scaled = self.lstm_scaler.transform(features)\n", "    \n", "            # Convert to tensor\n", "            seq_tensor = torch.tensor(X_scaled, dtype=torch.float32).unsqueeze(0)  # shape: (1, 30, 6)\n", "    \n", "            # Predict\n", "            self.lstm_model.eval()\n", "            with torch.no_grad():\n", "                pred = self.lstm_model(seq_tensor)  # shape: [1, 3]\n", "                pred_np = pred.cpu().numpy().flatten()  # shape: (3,)\n", "            \n", "            # Inverse scale prediction\n", "            pred_denorm = self.lstm_output_scaler.inverse_transform([pred_np])[0]  # shape: (3,)\n", "            \n", "            # Return all 3 values\n", "            return pred_denorm.tolist()\n", "    \n", "        except Exception as e:\n", "            logger.warning(f\"LSTM prediction failed: {e}\")\n", "            return [0.0, 0.0, 0.0]\n", "\n", "    def _take_action(self, action):\n", "        \"\"\"Execute trading action\"\"\"\n", "        if action == 0:  # Hold\n", "            return\n", "    \n", "        bid, ask = self._get_current_prices()\n", "        pip_value = 0.0001\n", "    \n", "        # Close existing position if needed\n", "        if self.position is not None:\n", "            self._close_position(bid, ask)\n", "    \n", "        # Trade size based on equity\n", "        self.trade_size = self.equity * TRADE_SIZE_PERCENT / 100\n", "    \n", "        # Margin check\n", "        margin_required = self.trade_size / LEVERAGE\n", "        if margin_required > self.equity:\n", "            logger.warning(\"Insufficient margin\")\n", "            return\n", "    \n", "        # SL and TP in dollar terms\n", "        sl_dollars = self.equity * 0.005  # 0.5%\n", "        tp_dollars = self.equity * 0.01   # 1.0%\n", "    \n", "        if action == 1:  # Buy\n", "            entry_price = ask\n", "            direction = 1\n", "        else:  # Sell\n", "            entry_price = bid\n", "            direction = -1\n", "    \n", "        sl_price = entry_price - (sl_dollars / self.trade_size * direction)\n", "        tp_price = entry_price + (tp_dollars / self.trade_size * direction)\n", "    \n", "        self.position = {\n", "            'direction': direction,\n", "            'entry_price': entry_price,\n", "            'size': self.trade_size,\n", "            'stop_loss': sl_price,\n", "            'take_profit': tp_price\n", "        }\n", "    \n", "        # Deduct commission\n", "        self.equity -= self.trade_size * self.commission\n", "    \n", "        # ? Logging\n", "        logger.info(f\"Placed trade: {'BUY' if direction == 1 else 'SELL'} @ {entry_price:.5f} | SL: {sl_price:.5f},TP: {tp_price:.5f}, Size: {self.trade_size:.2f}\")\n", "\n", "    def _close_position(self, bid, ask):\n", "        \"\"\"Close current position\"\"\"\n", "        if not self.position:\n", "            return\n", "\n", "        entry = self.position['entry_price']\n", "        size = self.position['size']\n", "        direction = self.position['direction']\n", "        \n", "        exit_price = ask if direction == 1 else bid\n", "        pnl = direction * (exit_price - entry) * size\n", "        \n", "        self.equity += pnl\n", "        self.equity_history.append(self.equity)\n", "        self.position = None\n", "\n", "    def _check_position_conditions(self, bid, ask):\n", "        \"\"\"Check SL/TP conditions\"\"\"\n", "        if not self.position:\n", "            return\n", "\n", "        direction = self.position['direction']\n", "        sl = self.position['stop_loss']\n", "        tp = self.position['take_profit']\n", "        \n", "        price = ask if direction == 1 else bid\n", "        \n", "        if (direction == 1 and price <= sl) or (direction == -1 and price >= sl):\n", "            self._close_position(bid, ask)\n", "            logger.info(\"Stop loss triggered\")\n", "        elif (direction == 1 and price >= tp) or (direction == -1 and price <= tp):\n", "            self._close_position(bid, ask)\n", "            logger.info(\"Take profit triggered\")\n", "\n", "    def _calculate_reward(self):\n", "        \"\"\"More balanced reward function\"\"\"\n", "        if not self.position:\n", "            return -0.05  # Small time penalty\n", "        \n", "        # Calculate components\n", "        price_change = self._get_price_change()  # Ensure this is normalized (e.g. % change)\n", "        equity_pct = (self.equity - 10000) / 10000  # [-1, 8] but typically small\n", "        drawdown_pct = max(0, (10000 - self.equity) / 10000)  # [0, 1]\n", "        \n", "        try:\n", "            sortino = self._calculate_sortino_ratio() or 0  # Handle None case\n", "        except:\n", "            sortino = 0\n", "        \n", "        # Balanced weights - all components now similar magnitude\n", "        reward = (\n", "            price_change * 2.0 +       # ~±2 for typical price moves\n", "            equity_pct * 1.0 -         # Direct equity influence\n", "            drawdown_pct * 1.5 +       # Penalize losses\n", "            sortino * 0.3              # Smaller risk adjustment\n", "        )\n", "        \n", "        # Softer clipping if needed\n", "        return np.clip(reward * 5.0, -5, 5)\n", "\n", "    def _calculate_sortino_ratio(self):\n", "        \"\"\"Calculate Sortino ratio for risk-adjusted rewards\"\"\"\n", "        if len(self.equity_history) < 2:\n", "            return 0\n", "\n", "        returns = np.diff(self.equity_history) / np.array(self.equity_history[:-1])\n", "        downside_returns = returns[returns < 0]\n", "        \n", "        if len(downside_returns) == 0:\n", "            return 0\n", "\n", "        downside_dev = np.std(downside_returns)\n", "        if downside_dev == 0:\n", "             return 0\n", "\n", "        excess_return = np.mean(returns)\n", "        return excess_return / downside_dev\n", "\n", "    def _get_price_change(self):\n", "        \"\"\"Get price change for current position\"\"\"\n", "        if not self.position:\n", "            return 0\n", "            \n", "        bid, ask = self._get_current_prices()\n", "        entry = self.position['entry_price']\n", "        direction = self.position['direction']\n", "        \n", "        current_price = ask if direction == 1 else bid\n", "        return (current_price - entry) * direction\n", "\n", "    def _get_market_data(api, account_id, instrument):\n", "        # Set up API stream\n", "        stream = PricingStream(accountID=account_id, params={\"instruments\": instrument})\n", "        \n", "        try:\n", "            # Read the first tick from the stream\n", "            for tick in api.request(stream):\n", "                if isinstance(tick, dict) and 'closeoutAsk' in tick and 'closeoutBid' in tick:\n", "                    ask = float(tick['closeoutAsk'])\n", "                    bid = float(tick['closeoutBid'])\n", "                    last_price = (ask + bid) / 2\n", "                    spread = ask - bid\n", "                    return ask,bid\n", "        except Exception as e:\n", "            print(f\"Error getting market data: {e}\")\n", "            return None, None\n", "        \n", "        return None, None  # Return None if no valid tick was receive\n", "\n", "    def _update_price_history(self, new_price):\n", "        \"\"\"Update price history\"\"\"\n", "        self.price_history.append(new_price)\n", "        if len(self.price_history) > 1000:\n", "            self.price_history.pop(0)\n", "\n", "    def _update_tech_indicators(self):\n", "        self.tech_indicators = self._init_tech_indicators(force_refresh=True)\n", "        self.indicator_cache = self.tech_indicators\n", "        if self.current_step % 500 == 0:\n", "            print(f\"[Step {self.current_step}] \")\n", "\n", "    def _get_current_prices(self):\n", "        \"\"\"Get current bid/ask prices\"\"\"\n", "        #return self.price_history[-1], self.price_history[-1] + 0.0002\n", "        price = self.data['c'].iloc[self.current_step]\n", "        bid = price\n", "        ask = price + 0.0002\n", "        return bid, ask\n", "    \n", "    def sentiment_model(self,data):\n", "        model_name = \"yiyanghkust/finbert-tone\"  # Pre-trained FinBERT model for financial sentiment analysis\n", "        tokenizer = BertTokenizer.from_pretrained(model_name)\n", "        model = BertForSequenceClassification.from_pretrained(model_name)\n", "        nlp = pipeline('sentiment-analysis',model=model,tokenizer=tokenizer)\n", "        result = nlp(data)\n", "        # Convert logits to probabilities\n", "        if result[0]['label'] =='Negative':\n", "                return -1\n", "        elif result[0]['label'] == 'Positive':\n", "                return 1\n", "        else:\n", "            return 0\n", "        \n", "    def sentiment_process(self):\n", "        response = requests.get(url)\n", "        # Check if the request was successful\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            json_data = data\n", "            data_list= json_data['data']\n", "            df = pd.DataFrame(data_list)\n", "            return df.text[0]\n", "        #return 'return no data'\n", "    \n", "    def sentiment_data(self):\n", "        data = self.sentiment_process()\n", "        sentiment_score= self.sentiment_model(data)\n", "        return sentiment_score\n", "        \n", "\n", "    def render(self, mode='human'):\n", "        \"\"\"Render environment state\"\"\"\n", "        bid, ask = self._get_current_prices()\n", "        print(f\"Step: {self.current_step} | Bid: {bid:.5f} | Ask: {ask:.5f} | \"\n", "              f\"Equity: {self.equity:.2f} | Position: {self.position}\")"]}, {"cell_type": "code", "execution_count": 5, "id": "8a51e148-886b-48d2-938a-ddc41d1e2943", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["? Loading pretrained LSTM model from saved_models/event_gru_3.pth\n"]}, {"ename": "RuntimeError", "evalue": "Error(s) in loading state_dict for LSTMPredictor:\n\tMissing key(s) in state_dict: \"gru.weight_ih_l0\", \"gru.weight_hh_l0\", \"gru.bias_ih_l0\", \"gru.bias_hh_l0\", \"gru.weight_ih_l1\", \"gru.weight_hh_l1\", \"gru.bias_ih_l1\", \"gru.bias_hh_l1\". \n\tUnexpected key(s) in state_dict: \"rnn.weight_ih_l0\", \"rnn.weight_hh_l0\", \"rnn.bias_ih_l0\", \"rnn.bias_hh_l0\", \"rnn.weight_ih_l1\", \"rnn.weight_hh_l1\", \"rnn.bias_ih_l1\", \"rnn.bias_hh_l1\". ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 113\u001b[0m\n\u001b[1;32m    111\u001b[0m         config \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mload(f)\n\u001b[1;32m    112\u001b[0m     trainer \u001b[38;5;241m=\u001b[39m LSTMTrainer(config\u001b[38;5;241m=\u001b[39mconfig)\n\u001b[0;32m--> 113\u001b[0m     trainer\u001b[38;5;241m.\u001b[39mmodel\u001b[38;5;241m.\u001b[39mload_state_dict(torch\u001b[38;5;241m.\u001b[39mload(LSTM_MODEL_PATH, map_location\u001b[38;5;241m=\u001b[39mtorch\u001b[38;5;241m.\u001b[39mdevice(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcpu\u001b[39m\u001b[38;5;124m\"\u001b[39m)))\n\u001b[1;32m    114\u001b[0m     trainer\u001b[38;5;241m.\u001b[39mmodel\u001b[38;5;241m.\u001b[39meval()\n\u001b[1;32m    115\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py:2593\u001b[0m, in \u001b[0;36mModule.load_state_dict\u001b[0;34m(self, state_dict, strict, assign)\u001b[0m\n\u001b[1;32m   2585\u001b[0m         error_msgs\u001b[38;5;241m.\u001b[39minsert(\n\u001b[1;32m   2586\u001b[0m             \u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m   2587\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing key(s) in state_dict: \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m   2588\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mk\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m missing_keys)\n\u001b[1;32m   2589\u001b[0m             ),\n\u001b[1;32m   2590\u001b[0m         )\n\u001b[1;32m   2592\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(error_msgs) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m-> 2593\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m   2594\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError(s) in loading state_dict for \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m   2595\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(error_msgs)\n\u001b[1;32m   2596\u001b[0m         )\n\u001b[1;32m   2597\u001b[0m     )\n\u001b[1;32m   2598\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _IncompatibleKeys(missing_keys, unexpected_keys)\n", "\u001b[0;31mRuntimeError\u001b[0m: Error(s) in loading state_dict for LSTMPredictor:\n\tMissing key(s) in state_dict: \"gru.weight_ih_l0\", \"gru.weight_hh_l0\", \"gru.bias_ih_l0\", \"gru.bias_hh_l0\", \"gru.weight_ih_l1\", \"gru.weight_hh_l1\", \"gru.bias_ih_l1\", \"gru.bias_hh_l1\". \n\tUnexpected key(s) in state_dict: \"rnn.weight_ih_l0\", \"rnn.weight_hh_l0\", \"rnn.bias_ih_l0\", \"rnn.bias_hh_l0\", \"rnn.weight_ih_l1\", \"rnn.weight_hh_l1\", \"rnn.bias_ih_l1\", \"rnn.bias_hh_l1\". "]}], "source": ["import os\n", "import torch\n", "import pandas as pd\n", "from stable_baselines3 import PPO\n", "from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv\n", "from stable_baselines3.common.callbacks import EvalCallback\n", "from stable_baselines3.common.monitor import Monitor\n", "from stable_baselines3.common.vec_env import VecNormalize, VecMonitor\n", "from sklearn.preprocessing import MinMaxScaler\n", "from torch import nn, optim\n", "from joblib import dump, load\n", "import json\n", "\n", "# --- Paths ---\n", "CSV_PATH = 'AUD_CHF_data.csv'\n", "PPO_MODEL_PATH = 'saved_models/ppo_model_AUD_CHF.zip'\n", "LSTM_MODEL_PATH = 'saved_models/event_gru_3.pth'\n", "SCALER_PATH = 'saved_models/scaler_AUD_CHF.joblib'\n", "CONFIG_PATH = \"saved_models/event_lstm_config.json\"\n", "TOTAL_TIMESTEPS = 1_000_000\n", "LOG_DIR = './logs'\n", "os.makedirs(LOG_DIR, exist_ok=True)\n", "os.makedirs('saved_models', exist_ok=True)\n", "\n", "class LSTMPredictor(nn.Mo<PERSON>le):\n", "    def __init__(self, input_size, hidden_size=64, num_layers=2, dropout=0.3):\n", "        super().__init__()\n", "        self.gru = nn.GRU(\n", "            input_size=input_size,\n", "            hidden_size=hidden_size,\n", "            num_layers=num_layers,\n", "            batch_first=True,\n", "            dropout=dropout if num_layers > 1 else 0.0\n", "        )\n", "        self.fc = nn.Linear(hidden_size, 3)\n", "\n", "    def forward(self, x):\n", "        _, hn = self.gru(x)\n", "        out = hn[-1]\n", "        return torch.sigmoid(self.fc(out))\n", "\n", "class LSTMTrainer:\n", "    def __init__(self, config):\n", "        # Safely remove 'mode' if present\n", "        clean_config = dict(config)\n", "        clean_config.pop('mode', None)\n", "\n", "        self.config = clean_config\n", "        self.model = LSTMPredictor(**clean_config)\n", "        self.criterion = nn.MS<PERSON><PERSON>()\n", "        self.optimizer = optim.<PERSON>(self.model.parameters(), lr=1e-3)\n", "\n", "    @staticmethod\n", "    def add_technical_indicators(df):\n", "        df['ema_fast'] = df['c'].ewm(span=12, adjust=False).mean()\n", "        df['ema_slow'] = df['c'].ewm(span=26, adjust=False).mean()\n", "        df['macd'] = df['ema_fast'] - df['ema_slow']\n", "        df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()\n", "        df['ema_fast_prev'] = df['ema_fast'].shift(1)\n", "        df['ema_slow_prev'] = df['ema_slow'].shift(1)\n", "        df['e2'] = ((df['ema_fast'] > df['ema_slow']) & (df['ema_fast_prev'] < df['ema_slow_prev'])).astype(int)\n", "        df.fillna(0, inplace=True)\n", "        return df\n", "\n", "    def load_data(self, csv_path):\n", "        df = pd.read_csv(csv_path)[['c']]\n", "        df = df.iloc[:-80000, :]\n", "        df = LSTMTrainer.add_technical_indicators(df)\n", "        feature_cols = ['c', 'macd', 'macd_signal', 'ema_fast', 'ema_slow', 'e2']\n", "        df = df[feature_cols].fillna(0)\n", "\n", "        input_scaler = MinMaxScaler()\n", "        X_scaled = input_scaler.fit_transform(df.values)\n", "        dump(input_scaler, SCALER_PATH)\n", "\n", "        close_prices = df['c'].values\n", "        rolling_std = pd.Series(close_prices).rolling(window=30).std().fillna(0).values\n", "\n", "        X, y = [], []\n", "        for i in range(30, len(X_scaled)):\n", "            seq = X_scaled[i - 30:i]\n", "            c = X_scaled[i, 0]\n", "            delta = rolling_std[i] if rolling_std[i] > 0 else 0.001\n", "            y.append([c, c - delta, c + delta])\n", "            X.append(seq)\n", "\n", "        y_scaled = MinMaxScaler().fit_transform(y)\n", "        dump(y_scaled, \"saved_models/lstm_output_scaler.joblib\")\n", "\n", "        return torch.tensor(X, dtype=torch.float32), torch.tensor(y_scaled, dtype=torch.float32)\n", "\n", "    def train(self, X, y, epochs=10, batch_size=64):\n", "        self.model.train()\n", "        for epoch in range(epochs):\n", "            permutation = torch.randperm(X.size(0))\n", "            for i in range(0, X.size(0), batch_size):\n", "                idx = permutation[i:i + batch_size]\n", "                batch_x, batch_y = X[idx], y[idx]\n", "                pred = self.model(batch_x)\n", "                loss = self.criterion(pred, batch_y)\n", "                self.optimizer.zero_grad()\n", "                loss.backward()\n", "                self.optimizer.step()\n", "            print(f\"Epoch {epoch + 1}/{epochs} - Loss: {loss.item():.6f}\")\n", "        return self.model\n", "\n", "# Load or train LSTM\n", "if os.path.exists(LSTM_MODEL_PATH) and os.path.exists(CONFIG_PATH):\n", "    print(f\"? Loading pretrained LSTM model from {LSTM_MODEL_PATH}\")\n", "    with open(CONFIG_PATH) as f:\n", "        config = json.load(f)\n", "    trainer = LSTMTrainer(config=config)\n", "    trainer.model.load_state_dict(torch.load(LSTM_MODEL_PATH, map_location=torch.device(\"cpu\")))\n", "    trainer.model.eval()\n", "else:\n", "    print(\"?? Training new LSTM model...\")\n", "    config = {\n", "        \"input_size\": 6,\n", "        \"hidden_size\": 64,\n", "        \"num_layers\": 2,\n", "        \"dropout\": 0.3\n", "    }\n", "    with open(CONFIG_PATH, \"w\") as f:\n", "        json.dump(config, f)\n", "    print(\"?? Saved LSTM config:\", CONFIG_PATH)\n", "\n", "    trainer = LSTMTrainer(config=config)\n", "    X, y = trainer.load_data(CSV_PATH)\n", "    trained_lstm = trainer.train(X, y, epochs=10)\n", "    torch.save(trained_lstm.state_dict(), LSTM_MODEL_PATH)\n", "    print(\"? LSTM model saved to:\", LSTM_MODEL_PATH)\n", "\n", "# PPO Training\n", "print(\"?? Launching PPO training...\")\n", "\n", "def get_lstm_from_config(path):\n", "    with open(CONFIG_PATH) as f:\n", "        cfg = json.load(f)\n", "    valid_keys = {'input_size', 'hidden_size', 'num_layers', 'dropout'}\n", "    clean_cfg = {k: cfg[k] for k in cfg if k in valid_keys}\n", "    model = LSTMPredictor(**clean_cfg)\n", "    model.load_state_dict(torch.load(LSTM_MODEL_PATH, map_location=torch.device(\"cpu\")))\n", "    model.eval()\n", "    return model\n", "\n", "#from forex_env import ForexTradingEnv\n", "env = DummyVecEnv([lambda: ForexTradingEnv()])\n", "env = VecNormalize(env, norm_obs=True, norm_reward=True, clip_obs=10)\n", "env = VecMonitor(env)\n", "\n", "eval_callback = EvalCallback(\n", "    env,\n", "    best_model_save_path='./logs/best_model/',\n", "    log_path='./logs/eval/',\n", "    eval_freq=1000000,\n", "    deterministic=True,\n", "    render=False\n", ")\n", "\n", "model = PPO(\n", "    policy=\"MlpPolicy\",\n", "    env=env,\n", "    device=\"cpu\",\n", "    learning_rate=1e-4,\n", "    n_steps=1024,\n", "    batch_size=128,\n", "    gamma=0.99,\n", "    gae_lambda=0.92,\n", "    clip_range=0.2,\n", "    ent_coef=0.01,\n", "    max_grad_norm=0.5,\n", "    policy_kwargs={\"net_arch\": [dict(pi=[256, 128], vf=[256, 128])]},\n", "    normalize_advantage=True,\n", "    tensorboard_log=\"./logs/tensorboard4/\",\n", "    verbose=1\n", ")\n", "\n", "model.learn(total_timesteps=TOTAL_TIMESTEPS, callback=eval_callback)\n", "model.save(PPO_MODEL_PATH)\n", "print(f\"? PPO model saved to: {PPO_MODEL_PATH}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "c7731a1e-5340-4ad8-b58a-6ccbe6d3ae0b", "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "# Load state_dict (don't load into model yet)\n", "checkpoint_path = 'saved_models/event_lstm_3.pth'\n", "state_dict = torch.load(checkpoint_path, map_location=torch.device('cpu'))\n", "\n", "# Print layer names and their shapes\n", "for key, value in state_dict.items():\n", "    print(f\"{key}: {value.shape}\")"]}, {"cell_type": "markdown", "id": "874377ba-c903-4793-a988-62fd911e8bb5", "metadata": {}, "source": ["##### tensorboard --logdir=./ppo_forex_tensorboard/"]}, {"cell_type": "code", "execution_count": null, "id": "f8848814-8426-4b4e-97fb-0da5fc5a00d4", "metadata": {}, "outputs": [], "source": ["tensorboard --logdir=./ppo_forex_trading_best_model/"]}, {"cell_type": "code", "execution_count": null, "id": "ed85d2fb-6475-4d7b-bd97-e7622fe5d76e", "metadata": {}, "outputs": [], "source": ["tensorboard --logdir=./logs/tensorboard4/"]}, {"cell_type": "code", "execution_count": null, "id": "8a9b4d84-f5ae-44f8-80c0-8c8718f8d8bc", "metadata": {}, "outputs": [], "source": ["tensorboard --logdir=./logs/tensorboard3/"]}, {"cell_type": "code", "execution_count": null, "id": "c0cb89dd-c78d-4fd9-a5b3-f1379d76533e", "metadata": {}, "outputs": [], "source": ["env = ForexTradingEnv()\n", "print(\"OBS:\", hasattr(env, \"observation_space\"))\n", "print(\"ACT:\", hasattr(env, \"action_space\"))"]}, {"cell_type": "code", "execution_count": null, "id": "1a6efc19-aff6-4fad-ae35-c83840b4a600", "metadata": {}, "outputs": [], "source": ["def make_env():\n", "    env = ForexTradingEnv()\n", "    print(\"OBS:\", hasattr(env, \"observation_space\"))\n", "    print(\"ACT:\", hasattr(env, \"action_space\"))\n", "    return Monitor(env)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b70c15f7-47be-4f83-9114-27e1235c9ee3", "metadata": {}, "outputs": [], "source": ["env = DummyVecEnv([make_env])"]}, {"cell_type": "markdown", "id": "64bd2f88-60b7-45b2-a39f-92fd5134bc26", "metadata": {}, "source": ["### live Inference"]}, {"cell_type": "code", "execution_count": null, "id": "59acd55c-3403-472d-9c1a-772122478e9b", "metadata": {}, "outputs": [], "source": ["import time\n", "import torch\n", "from stable_baselines3 import PPO\n", "from forex_env import ForexTradingEnv, LSTMPredictor\n", "from oandapyV20 import API\n", "import oandapyV20.endpoints.pricing as pricing\n", "\n", "\n", " Configuration\n", "ACCESS_TOKEN = \"5195a5aa58953e97d982e4c17ef761a7-2dfb4f0ca8739cc2cd9883d27e56532c\"\n", "#access_token=ACCESS_TOKEN \n", "ACCOUNT_ID = \"101-001-********-001\"\n", "account_id=ACCOUNT_ID \n", "INSTRUMENT = \"AUD_CHF\"\n", "instrument = INSTRUMENT\n", "# --- OANDA Config ---\n", "ACCESS_TOKEN = \"YOUR_PRACTICE_API_KEY\"\n", "ACCOUNT_ID = \"YOUR_PRACTICE_ACCOUNT_ID\"\n", "INSTRUMENT = \"AUD_CHF\"\n", "POLL_INTERVAL = 5  # seconds between price updates\n", "\n", "# --- Model Paths ---\n", "MODEL_PATH = \"./logs/ppo_forex_trading_model\"\n", "LSTM_PATH = \"./lstm_predictor_3.pth\"\n", "\n", "# --- Load LSTM ---\n", "lstm_model = LSTMPredictor(input_size=1)\n", "lstm_model.load_state_dict(torch.load(LSTM_PATH))\n", "lstm_model.eval()\n", "\n", "# --- OANDA API ---\n", "api = API(access_token=ACCESS_TOKEN)\n", "self.criterion = nn.MS<PERSON><PERSON>()\n", "self.optimizer = optim.<PERSON>(self.model.parameters(), lr=1e-3)\n", "\n", "\n", "def fetch_live_price():\n", "    r = pricing.PricingInfo(accountID=ACCOUNT_ID, params={\"instruments\": INSTRUMENT})\n", "    api.request(r)\n", "    prices = r.response['prices'][0]\n", "    bid = float(prices['bids'][0]['price'])\n", "    ask = float(prices['asks'][0]['price'])\n", "    return bid, ask\n", "\n", "# --- Initialize Environment ---\n", "env = ForexTradingEnv()\n", "env.lstm_model = lstm_model\n", "env.api = api\n", "env.account_id = ACCOUNT_ID\n", "env.instrument = INSTRUMENT\n", "env.simulate_sentiment = False  # Disable training noise\n", "\n", "\n", "# --- Load PPO Model ---\n", "model = PPO.load(MODEL_PATH)\n", "print(\"? PPO model loaded.\")\n", "\n", "# --- Begin Inference ---\n", "obs = env.reset()\n", "done = False\n", "step = 0\n", "MAX_STEPS = 300\n", "\n", "while not done and step < MAX_STEPS:\n", "    try:\n", "        # --- Fetch new market price and update ---\n", "        bid, ask = fetch_live_price()\n", "        mid_price = (bid + ask) / 2\n", "        env._update_price_history(mid_price)\n", "        env._update_tech_indicators()\n", "\n", "        # --- Predict action ---\n", "        action, _ = model.predict(obs, deterministic=True)\n", "\n", "        # --- Update live sentiment BEFORE step() ---\n", "        env.live_sentiment = env.sentiment_data()\n", "\n", "        # --- Execute action ---\n", "        obs, reward, done, info = env.step(action)\n", "        env.render()\n", "\n", "        step += 1\n", "        time.sleep(POLL_INTERVAL)\n", "\n", "    except Exception as e:\n", "        print(f\"?? Error during inference: {e}\")\n", "        time.sleep(POLL_INTERVAL)\n", "        continue\n", "\n", "print(\"? Live paper trading session complete.\")\n"]}, {"cell_type": "markdown", "id": "752f11ad-6a86-473c-8470-6590885cf810", "metadata": {}, "source": ["### LIVE TRADER"]}, {"cell_type": "code", "execution_count": null, "id": "340413e4-92e5-46a5-838b-695ecc87231d", "metadata": {}, "outputs": [], "source": ["import time\n", "import torch\n", "from stable_baselines3 import PPO\n", "from forex_env import ForexTradingEnv, LSTMPredictor\n", "from oandapyV20 import API\n", "import oandapyV20.endpoints.pricing as pricing\n", "import oandapyV20.endpoints.orders as orders\n", "import oandapyV20.endpoints.trades as trades\n", "\n", "# --- Configuration ---\n", "ACCESS_TOKEN = \"5195a5aa58953e97d982e4c17ef761a7-2dfb4f0ca8739cc2cd9883d27e56532c\"\n", "ACCOUNT_ID = \"101-001-********-001\"\n", "INSTRUMENT = \"AUD_CHF\"\n", "POLL_INTERVAL = 5  # seconds between price updates\n", "MODEL_PATH = \"./logs/ppo_forex_trading_model\"\n", "LSTM_PATH = \"./lstm_predictor.pth\"\n", "\n", "class ForexLiveEnv(ForexTradingEnv):\n", "    def __init__(self, api, account_id, instrument):\n", "        super().__init__()\n", "        self.api = api\n", "        self.account_id = account_id\n", "        self.instrument = instrument\n", "        self.simulate_sentiment = False\n", "\n", "        self.lstm_model = LSTMPredictor(input_size=1)\n", "        self.lstm_model.load_state_dict(torch.load(LSTM_PATH))\n", "        self.lstm_model.eval()\n", "\n", "        self.trade_id = None  # Track open trade\n", "\n", "    def fetch_live_price(self):\n", "        r = pricing.PricingInfo(accountID=self.account_id, params={\"instruments\": self.instrument})\n", "        self.api.request(r)\n", "        prices = r.response['prices'][0]\n", "        bid = float(prices['bids'][0]['price'])\n", "        ask = float(prices['asks'][0]['price'])\n", "        return bid, ask\n", "\n", "    def execute_trade(self, direction, units):\n", "        units = str(units if direction == 1 else -units)\n", "        order_data = {\n", "            \"order\": {\n", "                \"instrument\": self.instrument,\n", "                \"units\": units,\n", "                \"type\": \"MARKET\",\n", "                \"positionFill\": \"DEFAULT\"\n", "            }\n", "        }\n", "        r = orders.OrderCreate(accountID=self.account_id, data=order_data)\n", "        self.api.request(r)\n", "        self.trade_id = r.response['orderFillTransaction']['tradeOpened']['tradeID']\n", "        print(f\"? Executed {'BUY' if direction == 1 else 'SELL'} trade. Trade ID: {self.trade_id}\")\n", "\n", "    def close_trade(self):\n", "        if self.trade_id:\n", "            r = trades.TradeClose(accountID=self.account_id, tradeID=self.trade_id, data={})\n", "            self.api.request(r)\n", "            print(f\"?? Closed trade ID: {self.trade_id}\")\n", "            self.trade_id = None\n", "\n", "    def step(self, action):\n", "        current_time = time.time()\n", "        if not hasattr(self, '_last_indicator_update'):\n", "            self._last_indicator_update = 0\n", "\n", "        # Fetch new tick price every step (every few seconds)\n", "        bid, ask = self.fetch_live_price()\n", "        mid_price = (bid + ask) / 2\n", "        self._update_price_history(mid_price)\n", "\n", "        # Only update indicators once per hour\n", "        if current_time - self._last_indicator_update >= 3600:\n", "            self._update_tech_indicators()\n", "            self._last_indicator_update = current_time\n", "        # Update tech indicators only once per hour\n", "        current_time = time.time()\n", "        if not hasattr(self, '_last_indicator_update'):\n", "            self._last_indicator_update = 0\n", "        if current_time - self._last_indicator_update >= 3600:  # 3600 seconds = 1 hour\n", "            # self._update_tech_indicators()  # moved to hourly update logic\n", "            self._last_indicator_update = current_time\n", "        bid, ask = self.fetch_live_price()\n", "        mid_price = (bid + ask) / 2\n", "        self._update_price_history(mid_price)\n", "        # self._update_tech_indicators()  # moved to hourly update logic\n", "        self.live_sentiment = self.sentiment_data()\n", "\n", "        if action == 1:  # Buy\n", "            if self.trade_id:\n", "                self.close_trade()\n", "            self.execute_trade(direction=1, units=1000)\n", "        elif action == 2:  # Sell\n", "            if self.trade_id:\n", "                self.close_trade()\n", "            self.execute_trade(direction=-1, units=1000)\n", "\n", "        return super().step(action)\n", "\n", "# --- Initialize API and Environment ---\n", "api = API(access_token=ACCESS_TOKEN)\n", "env = ForexLiveEnv(api=api, account_id=ACCOUNT_ID, instrument=INSTRUMENT)\n", "\n", "# --- Load PPO Model ---\n", "model = PPO.load(MODEL_PATH)\n", "print(\"? PPO model loaded.\")\n", "\n", "# --- Begin Inference ---\n", "obs = env.reset()\n", "done = False\n", "step = 0\n", "MAX_STEPS = 300\n", "\n", "while not done and step < MAX_STEPS:\n", "    try:\n", "        action, _ = model.predict(obs, deterministic=True)\n", "        obs, reward, done, info = env.step(action)\n", "        env.render()\n", "        step += 1\n", "        time.sleep(POLL_INTERVAL)\n", "    except Exception as e:\n", "        print(f\"?? Error during inference: {e}\")\n", "        time.sleep(POLL_INTERVAL)\n", "        continue\n", "\n", "print(\"?? Live paper trading session complete.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}